"use client"; // Make it a client component to use hooks

import { usePathname } from "next/navigation"; // Import usePathname
import { cn } from "@/lib/utils";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";

interface SiteHeaderProps {
  className?: string;
}

// Helper function to get title from pathname
function getTitleFromPathname(pathname: string): string {
  const segments = pathname.split("/").filter(Boolean);
  const lastSegment = segments[segments.length - 1] || "home";

  switch (lastSegment) {
    case "home":
      return "Dashboard";
    case "metrics":
      return "Metrics";
    case "engage":
      return "Engage";
    case "analytics":
      return "Analytics";
    case "history":
      return "History";
    case "profile":
      return "Profile Setup";
    case "settings":
      return "Settings";
    case "help":
      return "Help & Resources";
    default:
      return "Dashboard"; // Fallback title
  }
}

export function SiteHeader({ className }: SiteHeaderProps) {
  const pathname = usePathname();
  const title = getTitleFromPathname(pathname);

  return (
    <header
      className={cn(
        "group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 transition-[width,height] ease-linear",
        className
      )}
    >
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
      </div>
    </header>
  );
}
