"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { MoreHorizontal, Download, Monitor, Smartphone, Laptop } from "lucide-react";

// Mock data based on the fourth image - Heartbeat widget
const heartbeatData = {
  title: "Safari Extension Users Since Launch",
  subtitle: "Weekly Safari Extension Users",
  totalUsers: {
    ios: "91.46K",
    mac: "8,391",
    win: "4,117"
  },
  breakdown: [
    { os: "ios", label: "iOS", value: 91460, color: "bg-blue-500" },
    { os: "mac", label: "macOS", value: 8391, color: "bg-red-500" },
    { os: "win", label: "Windows", value: 4117, color: "bg-cyan-500" },
    { os: "cros", label: "Chrome OS", value: 91, color: "bg-gray-400" },
    { os: "linux", label: "Linux", value: 22, color: "bg-gray-300" }
  ]
};

const totalUsers = heartbeatData.breakdown.reduce((sum, item) => sum + item.value, 0);

export function HeartbeatWidget() {
  const [selectedPlatforms, setSelectedPlatforms] = useState(["ios", "mac", "win"]);
  const [selectedTimeRange, setSelectedTimeRange] = useState("since_launch");

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-base font-medium">{heartbeatData.title}</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            {heartbeatData.subtitle}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Download className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Platform Controls */}
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Platforms:</span>
              <ToggleGroup
                type="multiple"
                value={selectedPlatforms}
                onValueChange={setSelectedPlatforms}
                className="justify-start"
              >
                <ToggleGroupItem value="ios" aria-label="iOS">
                  <Smartphone className="h-4 w-4" />
                  <span className="ml-1">iOS</span>
                </ToggleGroupItem>
                <ToggleGroupItem value="mac" aria-label="macOS">
                  <Laptop className="h-4 w-4" />
                  <span className="ml-1">macOS</span>
                </ToggleGroupItem>
                <ToggleGroupItem value="win" aria-label="Windows">
                  <Monitor className="h-4 w-4" />
                  <span className="ml-1">Windows</span>
                </ToggleGroupItem>
              </ToggleGroup>
            </div>

            <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Time range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="since_launch">Since Launch</SelectItem>
                <SelectItem value="last_30_days">Last 30 days</SelectItem>
                <SelectItem value="last_7_days">Last 7 days</SelectItem>
                <SelectItem value="yesterday">Yesterday</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Large metrics display */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                {heartbeatData.totalUsers.ios}
              </div>
              <div className="text-xs text-muted-foreground font-medium">iOS</div>
            </div>
            <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
              <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-1">
                {heartbeatData.totalUsers.mac}
              </div>
              <div className="text-xs text-muted-foreground font-medium">macOS</div>
            </div>
            <div className="p-4 bg-cyan-500/10 border border-cyan-500/20 rounded-lg">
              <div className="text-3xl font-bold text-cyan-600 dark:text-cyan-400 mb-1">
                {heartbeatData.totalUsers.win}
              </div>
              <div className="text-xs text-muted-foreground font-medium">Windows</div>
            </div>
          </div>

          {/* Operating System breakdown */}
          <div className="space-y-3">
            <div className="text-sm font-medium text-muted-foreground">Operating System Breakdown</div>

            <div className="space-y-2">
              {heartbeatData.breakdown.map((item, index) => {
                const percentage = ((item.value / totalUsers) * 100).toFixed(1);
                return (
                  <div key={index} className="flex items-center justify-between p-3 hover:bg-muted/30 rounded-lg transition-colors border border-transparent hover:border-border">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded ${item.color}`}></div>
                      <span className="text-sm font-medium text-foreground">{item.label}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-sm text-muted-foreground">{percentage}%</span>
                      <span className="text-sm font-semibold min-w-[60px] text-right text-foreground">
                        {item.value.toLocaleString()}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Total summary */}
          <div className="pt-4 border-t border-border">
            <div className="flex justify-between items-center p-4 bg-muted/30 border rounded-lg">
              <span className="font-medium text-foreground">Total Users</span>
              <span className="text-lg font-bold text-foreground">
                {totalUsers.toLocaleString()}
              </span>
            </div>
          </div>

          {/* Heartbeat metric info */}
          <div className="text-xs text-muted-foreground text-center bg-muted/20 p-2 rounded">
            heartbeat [Distinct Count of phia_id]
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
