"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MoreHorizontal, Download, TrendingUp, Calendar } from "lucide-react";

// Mock data based on the image - onboarding permissions chart
const onboardingData = {
  title: "Enabled Permissions % First-Time Run",
  metric: "% Users that gave ALL permissions for Safari Onboarding",
  timeRange: "Jun 24 - Jul 1",
  chartData: [
    { date: "Jun 24", percentage: 32.0 },
    { date: "Jun 25", percentage: 31.5 },
    { date: "Jun 26", percentage: 33.2 },
    { date: "Jun 27", percentage: 34.1 },
    { date: "Jun 28", percentage: 31.8 },
    { date: "Jun 29", percentage: 32.9 },
    { date: "Jun 30", percentage: 33.5 },
    { date: "Jul 1", percentage: 32.7 }
  ],
  currentValue: 32.56,
  trend: "up"
};

export function OnboardingPermissionsWidget() {
  const [selectedTimeRange, setSelectedTimeRange] = useState("7d");
  const [selectedMetric, setSelectedMetric] = useState("all_permissions");
  const [activeView, setActiveView] = useState("chart");

  const maxValue = Math.max(...onboardingData.chartData.map(d => d.percentage));
  const minValue = Math.min(...onboardingData.chartData.map(d => d.percentage));

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-base font-medium">{onboardingData.title}</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            {onboardingData.timeRange}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Download className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Controls */}
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="14d">Last 14 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedMetric} onValueChange={setSelectedMetric}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Metric" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_permissions">All Permissions</SelectItem>
                  <SelectItem value="partial_permissions">Partial Permissions</SelectItem>
                  <SelectItem value="no_permissions">No Permissions</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Tabs value={activeView} onValueChange={setActiveView} className="w-auto">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="chart">Chart</TabsTrigger>
                <TabsTrigger value="table">Table</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <Tabs value={activeView} className="w-full">
            <TabsContent value="chart" className="space-y-4">
              {/* Chart area */}
              <div className="relative h-80 bg-muted/10 rounded-lg p-6">
                {/* Y-axis labels */}
                <div className="absolute left-2 top-6 bottom-12 flex flex-col justify-between text-xs text-muted-foreground">
                  <span>36%</span>
                  <span>33%</span>
                  <span>30%</span>
                </div>

                {/* Chart content */}
                <div className="ml-10 mr-4 h-60 relative">
                  {/* Grid lines */}
                  <div className="absolute inset-0">
                    <div className="absolute top-0 left-0 right-0 h-px bg-border/30"></div>
                    <div className="absolute top-1/2 left-0 right-0 h-px bg-border/30"></div>
                    <div className="absolute bottom-0 left-0 right-0 h-px bg-border/30"></div>
                  </div>

                  {/* Data bars container */}
                  <div className="absolute bottom-0 left-0 right-0 h-full flex items-end justify-between gap-1">
                    {onboardingData.chartData.map((point, index) => {
                      // Calculate height based on percentage (30-36% range)
                      const minPercent = 30;
                      const maxPercent = 36;
                      const normalizedValue = (point.percentage - minPercent) / (maxPercent - minPercent);
                      const barHeight = Math.max(normalizedValue * 240, 20); // 240px max height, 20px minimum

                      return (
                        <div key={index} className="flex-1 flex justify-center group relative">
                          <div className="relative">
                            <div
                              className="w-8 bg-primary hover:bg-primary/80 transition-all duration-200 rounded-t-sm cursor-pointer"
                              style={{ height: `${barHeight}px` }}
                              title={`${point.date}: ${point.percentage}%`}
                            />
                            {/* Tooltip on hover */}
                            <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-popover text-popover-foreground text-xs px-2 py-1 rounded shadow-lg border whitespace-nowrap z-20">
                              {point.percentage}%
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* X-axis labels */}
                <div className="absolute bottom-2 left-10 right-4 flex justify-between text-xs text-muted-foreground">
                  {onboardingData.chartData.map((point, index) => (
                    <span key={index} className="flex-1 text-center">
                      {point.date}
                    </span>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="table" className="space-y-4">
              {/* Data table */}
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-3 h-3 bg-primary rounded"></div>
                  <span className="font-medium text-foreground">{onboardingData.metric}</span>
                </div>

                <div className="grid grid-cols-4 gap-4 text-xs font-medium text-muted-foreground border-b border-border pb-3">
                  <span>Date</span>
                  <span className="text-right">Total Users</span>
                  <span className="text-right">Enabled Permissions</span>
                  <span className="text-right">Percentage</span>
                </div>

                {onboardingData.chartData.map((point, index) => {
                  const totalUsers = 640 + (index * 15); // Varying user counts
                  const enabledUsers = Math.round(totalUsers * (point.percentage / 100));

                  return (
                    <div key={index} className="grid grid-cols-4 gap-4 text-sm py-2 hover:bg-muted/30 rounded-md transition-colors">
                      <span className="text-foreground font-medium">{point.date}</span>
                      <span className="text-right text-muted-foreground">{totalUsers.toLocaleString()}</span>
                      <span className="text-right">
                        <span className="bg-primary/10 text-primary px-2 py-1 rounded text-center font-medium">
                          {enabledUsers}
                        </span>
                      </span>
                      <span className="text-right">
                        <span className="bg-primary/20 text-primary px-2 py-1 rounded text-center font-medium">
                          {point.percentage}%
                        </span>
                      </span>
                    </div>
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );
}
