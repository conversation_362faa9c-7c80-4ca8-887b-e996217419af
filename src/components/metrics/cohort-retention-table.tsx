"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MoreHorizontal, Download, Filter, Calendar } from "lucide-react";
import { cn } from "@/lib/utils";

// Mock data based on the cohort retention table image
const cohortData = [
  {
    cohortWeekStart: "06/24/25",
    newUsersFullyOnboarded: 10714,
    week1RetainedPercent: 24.40,
    week2RetainedPercent: 22.18,
    week3RetainedPercent: 19.74,
    week4RetainedPercent: 16.77,
    week5RetainedPercent: 13.78
  },
  {
    cohortWeekStart: "06/17/25",
    newUsersFullyOnboarded: 9896,
    week1RetainedPercent: 21.87,
    week2RetainedPercent: 20.15,
    week3RetainedPercent: 16.34,
    week4RetainedPercent: 13.98,
    week5RetainedPercent: 8.80
  },
  {
    cohortWeekStart: "06/10/25",
    newUsersFullyOnboarded: 4558,
    week1RetainedPercent: 22.75,
    week2RetainedPercent: 19.85,
    week3RetainedPercent: 16.17,
    week4RetainedPercent: 13.84,
    week5RetainedPercent: 7.53
  },
  {
    cohortWeekStart: "06/03/25",
    newUsersFullyOnboarded: 2516,
    week1RetainedPercent: 24.85,
    week2RetainedPercent: 19.12,
    week3RetainedPercent: 14.23,
    week4RetainedPercent: 6.74,
    week5RetainedPercent: 0
  },
  {
    cohortWeekStart: "05/27/25",
    newUsersFullyOnboarded: 4318,
    week1RetainedPercent: 22.29,
    week2RetainedPercent: 9.46,
    week3RetainedPercent: 7.40,
    week4RetainedPercent: 0,
    week5RetainedPercent: 0
  },
  {
    cohortWeekStart: "05/20/25",
    newUsersFullyOnboarded: 4484,
    week1RetainedPercent: 19.30,
    week2RetainedPercent: 10.02,
    week3RetainedPercent: 0,
    week4RetainedPercent: 0,
    week5RetainedPercent: 0
  },
  {
    cohortWeekStart: "05/13/25",
    newUsersFullyOnboarded: 5541,
    week1RetainedPercent: 0,
    week2RetainedPercent: 0,
    week3RetainedPercent: 0,
    week4RetainedPercent: 0,
    week5RetainedPercent: 0
  }
];

// Helper function to get color based on retention percentage
const getRetentionColor = (percentage: number): string => {
  if (percentage === 0) return "bg-muted text-muted-foreground";
  if (percentage < 10) return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800";
  if (percentage < 15) return "bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-800";
  if (percentage < 20) return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800";
  if (percentage < 25) return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800";
  return "bg-green-200 dark:bg-green-900/50 text-green-800 dark:text-green-200 border border-green-300 dark:border-green-700";
};

export function CohortRetentionTable() {
  const [selectedCohortType, setSelectedCohortType] = useState("weekly");
  const [selectedDateRange, setSelectedDateRange] = useState("last_8_weeks");
  const [selectedMetric, setSelectedMetric] = useState("retention_rate");
  const [activeView, setActiveView] = useState("table");

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-base font-medium">Cohort Retention Analysis</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            {selectedCohortType.charAt(0).toUpperCase() + selectedCohortType.slice(1)} retention analysis by cohort
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Download className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Cohort Filters */}
          <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
            <div className="flex flex-wrap items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={selectedCohortType} onValueChange={setSelectedCohortType}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Cohort type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                </SelectContent>
              </Select>

              <Calendar className="h-4 w-4 text-muted-foreground ml-2" />
              <Select value={selectedDateRange} onValueChange={setSelectedDateRange}>
                <SelectTrigger className="w-[160px]">
                  <SelectValue placeholder="Date range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last_4_weeks">Last 4 weeks</SelectItem>
                  <SelectItem value="last_8_weeks">Last 8 weeks</SelectItem>
                  <SelectItem value="last_12_weeks">Last 12 weeks</SelectItem>
                  <SelectItem value="last_6_months">Last 6 months</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedMetric} onValueChange={setSelectedMetric}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Metric" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="retention_rate">Retention %</SelectItem>
                  <SelectItem value="user_count">User Count</SelectItem>
                  <SelectItem value="churn_rate">Churn Rate</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Tabs value={activeView} onValueChange={setActiveView} className="w-auto">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="table">Table</TabsTrigger>
                <TabsTrigger value="heatmap">Heatmap</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <Tabs value={activeView} className="w-full">
            <TabsContent value="table" className="space-y-4">
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
            <thead>
              <tr className="border-b border-border bg-muted/30">
                <th className="text-left p-3 font-medium text-sm text-foreground">Cohort (Week Start)</th>
                <th className="text-right p-3 font-medium text-sm text-foreground">New users fully onboarded</th>
                <th className="text-center p-3 font-medium text-sm text-foreground">Week 1 Retained %</th>
                <th className="text-center p-3 font-medium text-sm text-foreground">Week 2 Retained %</th>
                <th className="text-center p-3 font-medium text-sm text-foreground">Week 3 Retained %</th>
                <th className="text-center p-3 font-medium text-sm text-foreground">Week 4 Retained %</th>
                <th className="text-center p-3 font-medium text-sm text-foreground">Week 5 Retained %</th>
              </tr>
            </thead>
            <tbody>
              {cohortData.map((cohort, index) => (
                <tr key={cohort.cohortWeekStart} className="border-b border-border hover:bg-muted/20 transition-colors">
                  <td className="p-3 text-sm font-medium text-foreground">{cohort.cohortWeekStart}</td>
                  <td className="p-3 text-sm text-right text-muted-foreground">{cohort.newUsersFullyOnboarded.toLocaleString()}</td>
                  <td className="p-3 text-center">
                    {cohort.week1RetainedPercent > 0 ? (
                      <span className={cn(
                        "inline-block px-2 py-1 rounded text-xs font-medium min-w-[50px]",
                        getRetentionColor(cohort.week1RetainedPercent)
                      )}>
                        {cohort.week1RetainedPercent.toFixed(2)}%
                      </span>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </td>
                  <td className="p-3 text-center">
                    {cohort.week2RetainedPercent > 0 ? (
                      <span className={cn(
                        "inline-block px-2 py-1 rounded text-xs font-medium min-w-[50px]",
                        getRetentionColor(cohort.week2RetainedPercent)
                      )}>
                        {cohort.week2RetainedPercent.toFixed(2)}%
                      </span>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </td>
                  <td className="p-3 text-center">
                    {cohort.week3RetainedPercent > 0 ? (
                      <span className={cn(
                        "inline-block px-2 py-1 rounded text-xs font-medium min-w-[50px]",
                        getRetentionColor(cohort.week3RetainedPercent)
                      )}>
                        {cohort.week3RetainedPercent.toFixed(2)}%
                      </span>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </td>
                  <td className="p-3 text-center">
                    {cohort.week4RetainedPercent > 0 ? (
                      <span className={cn(
                        "inline-block px-2 py-1 rounded text-xs font-medium min-w-[50px]",
                        getRetentionColor(cohort.week4RetainedPercent)
                      )}>
                        {cohort.week4RetainedPercent.toFixed(2)}%
                      </span>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </td>
                  <td className="p-3 text-center">
                    {cohort.week5RetainedPercent > 0 ? (
                      <span className={cn(
                        "inline-block px-2 py-1 rounded text-xs font-medium min-w-[50px]",
                        getRetentionColor(cohort.week5RetainedPercent)
                      )}>
                        {cohort.week5RetainedPercent.toFixed(2)}%
                      </span>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
            </TabsContent>

            <TabsContent value="heatmap" className="space-y-4">
              <div className="h-96 bg-muted/20 rounded-lg border p-4">
                <h4 className="text-sm font-medium mb-4">Retention Heatmap</h4>
                <div className="grid grid-cols-6 gap-1 h-80">
                  {/* Header row */}
                  <div className="text-xs font-medium text-muted-foreground p-2">Cohort</div>
                  <div className="text-xs font-medium text-muted-foreground p-2 text-center">Week 1</div>
                  <div className="text-xs font-medium text-muted-foreground p-2 text-center">Week 2</div>
                  <div className="text-xs font-medium text-muted-foreground p-2 text-center">Week 3</div>
                  <div className="text-xs font-medium text-muted-foreground p-2 text-center">Week 4</div>
                  <div className="text-xs font-medium text-muted-foreground p-2 text-center">Week 5</div>

                  {/* Data rows */}
                  {cohortData.map((cohort, rowIndex) => (
                    <React.Fragment key={rowIndex}>
                      <div className="text-xs p-2 font-medium">{cohort.cohortWeekStart}</div>
                      <div className={cn(
                        "text-xs p-2 text-center rounded",
                        getRetentionColor(cohort.week1RetainedPercent)
                      )}>
                        {cohort.week1RetainedPercent > 0 ? `${cohort.week1RetainedPercent.toFixed(1)}%` : '-'}
                      </div>
                      <div className={cn(
                        "text-xs p-2 text-center rounded",
                        getRetentionColor(cohort.week2RetainedPercent)
                      )}>
                        {cohort.week2RetainedPercent > 0 ? `${cohort.week2RetainedPercent.toFixed(1)}%` : '-'}
                      </div>
                      <div className={cn(
                        "text-xs p-2 text-center rounded",
                        getRetentionColor(cohort.week3RetainedPercent)
                      )}>
                        {cohort.week3RetainedPercent > 0 ? `${cohort.week3RetainedPercent.toFixed(1)}%` : '-'}
                      </div>
                      <div className={cn(
                        "text-xs p-2 text-center rounded",
                        getRetentionColor(cohort.week4RetainedPercent)
                      )}>
                        {cohort.week4RetainedPercent > 0 ? `${cohort.week4RetainedPercent.toFixed(1)}%` : '-'}
                      </div>
                      <div className={cn(
                        "text-xs p-2 text-center rounded",
                        getRetentionColor(cohort.week5RetainedPercent)
                      )}>
                        {cohort.week5RetainedPercent > 0 ? `${cohort.week5RetainedPercent.toFixed(1)}%` : '-'}
                      </div>
                    </React.Fragment>
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );
}
