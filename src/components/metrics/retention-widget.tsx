"use client";

import React, { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MoreHorizontal, Download, TrendingDown, Calendar, BarChart3 } from "lucide-react";

// Mock data based on the fifth image - Retention curve
const retentionData = {
  title: "Week 9 Retention (6/19 - 6/28)",
  subtitle: "Churn",
  description: "The number of select calendar weeks later your Users come back and did it.",
  chartData: [
    { week: "Week", percentage: 92.3 },
    { week: "Week 1", percentage: 82.5 }
  ],
  tableData: [
    { date: "Jun 19, 2025", totalProfiles: 640, week1: "91.26%", weekValue: "63.35%" },
    { date: "Jun 20, 2025", totalProfiles: 889, week1: "97.55%", weekValue: "51.86%" },
    { date: "Jun 21, 2025", totalProfiles: 1118, week1: "97.32%", weekValue: "85.24%" },
    { date: "Jun 22, 2025", totalProfiles: 1435, week1: "97.56%", weekValue: "86.3%" }
  ]
};

export function RetentionWidget() {
  const [selectedWeek, setSelectedWeek] = useState("week_9");
  const [selectedPeriod, setSelectedPeriod] = useState("weekly");
  const [activeView, setActiveView] = useState("curve");

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-base font-medium">Retention Analysis</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            {retentionData.subtitle} - {selectedWeek.replace('_', ' ').toUpperCase()}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Download className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Retention Controls */}
          <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
            <div className="flex flex-wrap items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <Select value={selectedWeek} onValueChange={setSelectedWeek}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Week" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week_9">Week 9</SelectItem>
                  <SelectItem value="week_8">Week 8</SelectItem>
                  <SelectItem value="week_7">Week 7</SelectItem>
                  <SelectItem value="week_6">Week 6</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Tabs value={activeView} onValueChange={setActiveView} className="w-auto">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="curve">Curve</TabsTrigger>
                <TabsTrigger value="table">Table</TabsTrigger>
                <TabsTrigger value="cohort">Cohort</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <Tabs value={activeView} className="w-full">
            <TabsContent value="curve" className="space-y-6">
              {/* Retention curve chart */}
          <div className="relative h-64 bg-muted/20 border rounded-lg p-4">
            <div className="absolute inset-4">
              {/* Y-axis labels */}
              <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-muted-foreground">
                <span>100%</span>
                <span>90%</span>
                <span>80%</span>
                <span>70%</span>
                <span>60%</span>
              </div>

              {/* Chart area */}
              <div className="ml-8 h-full relative">
                {/* Retention curve line */}
                <svg className="absolute inset-0 w-full h-full">
                  <defs>
                    <linearGradient id="retentionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="hsl(var(--primary))" />
                      <stop offset="100%" stopColor="hsl(var(--primary) / 0.6)" />
                    </linearGradient>
                  </defs>
                  <path
                    d="M 0 20 Q 50 25 100 45 Q 150 55 200 65 Q 250 75 300 85"
                    stroke="url(#retentionGradient)"
                    strokeWidth="3"
                    fill="none"
                    className="drop-shadow-sm"
                  />
                  {/* Data points */}
                  <circle cx="0" cy="20" r="4" fill="hsl(var(--primary))" />
                  <circle cx="300" cy="85" r="4" fill="hsl(var(--primary) / 0.6)" />
                </svg>

                {/* X-axis labels */}
                <div className="absolute bottom-0 left-0 right-0 flex justify-between text-xs text-muted-foreground">
                  <span>Week</span>
                  <span>Week 1</span>
                </div>
              </div>
            </div>

            {/* Chart description */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground text-center max-w-xs">
              {retentionData.description}
            </div>
          </div>

          {/* Data table */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm">
              <div className="w-3 h-3 bg-primary rounded"></div>
              <span className="font-medium text-foreground">Weighted Average</span>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border text-xs font-medium text-muted-foreground">
                    <th className="text-left p-3">Date</th>
                    <th className="text-right p-3">Total Profiles</th>
                    <th className="text-right p-3">&lt; 1 Week</th>
                    <th className="text-right p-3">Week 1</th>
                  </tr>
                </thead>
                <tbody>
                  {retentionData.tableData.map((row, index) => (
                    <tr key={index} className="border-b border-border hover:bg-muted/30 transition-colors">
                      <td className="p-3 text-sm font-medium text-foreground">{row.date}</td>
                      <td className="p-3 text-sm text-right text-muted-foreground">{row.totalProfiles.toLocaleString()}</td>
                      <td className="p-3 text-right">
                        <span className="inline-block px-2 py-1 rounded text-xs font-medium bg-primary/10 text-primary border border-primary/20">
                          {row.week1}
                        </span>
                      </td>
                      <td className="p-3 text-right">
                        <span className="inline-block px-2 py-1 rounded text-xs font-medium bg-primary/20 text-primary border border-primary/30">
                          {row.weekValue}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Retention criteria info */}
          <div className="text-xs text-muted-foreground bg-muted/30 border p-3 rounded-lg">
            <div className="font-medium mb-1 text-foreground">Retention Criteria</div>
            <div>On: Each Calendar Week</div>
            <div>Save as New Behavior</div>
          </div>
            </TabsContent>

            <TabsContent value="table" className="space-y-4">
              <div className="h-96 bg-muted/20 rounded-lg border p-4 overflow-y-auto">
                <h4 className="text-sm font-medium mb-4">Detailed Retention Data</h4>
                <div className="space-y-2">
                  <div className="grid grid-cols-4 gap-4 text-xs font-medium text-muted-foreground border-b pb-2">
                    <span>Date</span>
                    <span className="text-right">Total Users</span>
                    <span className="text-right">Retained Week 1</span>
                    <span className="text-right">Retention Rate</span>
                  </div>
                  {retentionData.tableData.map((row, index) => (
                    <div key={index} className="grid grid-cols-4 gap-4 text-sm py-2 hover:bg-muted/30 rounded">
                      <span className="font-medium">{row.date}</span>
                      <span className="text-right">{row.totalProfiles.toLocaleString()}</span>
                      <span className="text-right">{Math.round(row.totalProfiles * 0.85).toLocaleString()}</span>
                      <span className="text-right font-medium">{row.week1}</span>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="cohort" className="space-y-4">
              <div className="h-96 bg-muted/20 rounded-lg border p-4">
                <h4 className="text-sm font-medium mb-4">Cohort Analysis</h4>
                <div className="grid grid-cols-5 gap-2 h-80">
                  {/* Header */}
                  <div className="text-xs font-medium text-muted-foreground p-2">Cohort</div>
                  <div className="text-xs font-medium text-muted-foreground p-2 text-center">Week 0</div>
                  <div className="text-xs font-medium text-muted-foreground p-2 text-center">Week 1</div>
                  <div className="text-xs font-medium text-muted-foreground p-2 text-center">Week 2</div>
                  <div className="text-xs font-medium text-muted-foreground p-2 text-center">Week 3</div>

                  {/* Sample cohort data */}
                  {retentionData.tableData.map((row, index) => (
                    <React.Fragment key={index}>
                      <div className="text-xs p-2 font-medium">{row.date}</div>
                      <div className="text-xs p-2 text-center bg-green-100 dark:bg-green-900/30 rounded">100%</div>
                      <div className="text-xs p-2 text-center bg-yellow-100 dark:bg-yellow-900/30 rounded">{row.week1}</div>
                      <div className="text-xs p-2 text-center bg-orange-100 dark:bg-orange-900/30 rounded">{row.weekValue}</div>
                      <div className="text-xs p-2 text-center bg-red-100 dark:bg-red-900/30 rounded">
                        {(parseFloat(row.weekValue.replace('%', '')) * 0.7).toFixed(1)}%
                      </div>
                    </React.Fragment>
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );
}
