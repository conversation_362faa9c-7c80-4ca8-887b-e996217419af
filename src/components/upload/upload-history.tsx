"use client";

import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MoreHorizontalIcon,
  EyeIcon,
  RefreshCwIcon,
  TrashIcon,
  DownloadIcon,
  AlertCircleIcon
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface UploadRecord {
  id: string;
  original_filename: string;
  upload_date: string;
  status: string;
  platform: string;
  total_rows: number;
  processed_rows: number;
  successful_rows: number;
  error_rows: number;
  duplicate_rows: number;
  file_size: number;
  processing_started_at?: string;
  processing_completed_at?: string;
  error_message?: string;
}

export function UploadHistory() {
  const [uploads, setUploads] = useState<UploadRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchUploads();
  }, [page]);

  const fetchUploads = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/upload?page=${page}&limit=10`);

      if (!response.ok) {
        throw new Error('Failed to fetch uploads');
      }

      const data = await response.json();
      setUploads(data.data);
      setTotalPages(data.pagination.totalPages);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch uploads');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: { [key: string]: { variant: any; className?: string } } = {
      uploaded: { variant: 'secondary' },
      processing: { variant: 'default', className: 'bg-blue-100 text-blue-800' },
      completed: { variant: 'default', className: 'bg-green-100 text-green-800' },
      failed: { variant: 'destructive' },
      cancelled: { variant: 'outline' }
    };

    const config = variants[status] || { variant: 'outline' };

    return (
      <Badge variant={config.variant} className={config.className}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleViewErrors = (uploadId: string) => {
    // Open errors modal
    // TODO: Implement errors modal
  };

  if (loading && uploads.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <Skeleton className="h-4 w-[200px]" />
            <Skeleton className="h-4 w-[100px]" />
            <Skeleton className="h-4 w-[80px]" />
            <Skeleton className="h-4 w-[120px]" />
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircleIcon className="h-4 w-4" />
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (uploads.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No uploads found. Upload your first CSV file to get started.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Filename</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Upload Date</TableHead>
              <TableHead>Rows</TableHead>
              <TableHead>Success Rate</TableHead>
              <TableHead>Duplicates</TableHead>
              <TableHead>Size</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {uploads.map((upload) => {
              // Calculate success rate excluding duplicates (since duplicates are correctly handled)
              const processableRows = upload.total_rows - (upload.duplicate_rows || 0);
              const successRate = processableRows > 0
                ? Math.round((upload.successful_rows / processableRows) * 100)
                : 0;

              return (
                <TableRow key={upload.id}>
                  <TableCell className="font-medium">
                    <div>
                      <p className="truncate max-w-[200px]">{upload.original_filename}</p>
                      <p className="text-xs text-muted-foreground">{upload.platform}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(upload.status)}
                  </TableCell>
                  <TableCell>
                    {formatDate(upload.upload_date)}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <p>{upload.total_rows} total</p>
                      {upload.processed_rows > 0 && (
                        <p className="text-xs text-muted-foreground">
                          {upload.processed_rows} processed
                        </p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {upload.status === 'completed' ? (
                      <div className="text-sm">
                        <p className={successRate >= 90 ? 'text-green-600' : successRate >= 70 ? 'text-yellow-600' : 'text-red-600'}>
                          {successRate}%
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {upload.successful_rows}/{processableRows}
                        </p>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {upload.status === 'completed' ? (
                      <div className="text-sm">
                        <p className="text-blue-600">
                          {upload.duplicate_rows || 0}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          skipped
                        </p>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {formatFileSize(upload.file_size)}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontalIcon className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {upload.error_rows > 0 && (
                          <DropdownMenuItem onClick={() => handleViewErrors(upload.id)}>
                            <AlertCircleIcon className="mr-2 h-4 w-4" />
                            View Errors ({upload.error_rows})
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Page {page} of {totalPages}
          </p>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page <= 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page >= totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
