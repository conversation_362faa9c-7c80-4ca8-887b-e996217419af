import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with service role for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

interface FilterOption {
  value: string;
  label: string;
  count: number;
}

interface FilterOptionsResponse {
  platforms: FilterOption[];
  merchants: FilterOption[];
  statuses: FilterOption[];
  networks: FilterOption[];
  transactionTypes: FilterOption[];
  error?: string;
}

// Helper function to get filter counts using database function (super efficient!)
async function getFilterCounts(filterType: string, limit?: number): Promise<FilterOption[]> {
  try {
    console.log(`Fetching filter counts for ${filterType} using database function...`);

    // Use our custom database function for maximum efficiency
    const { data, error } = await supabaseAdmin
      .rpc('get_filter_options')
      .eq('filter_type', filterType);

    if (error) {
      console.error(`Error calling database function for ${filterType}:`, error);
      return [];
    }

    const result = (data || [])
      .map((item: any) => ({
        value: item.value,
        label: formatLabel(filterType, item.value),
        count: parseInt(item.count)
      }))
      .sort((a: FilterOption, b: FilterOption) => b.count - a.count)
      .slice(0, limit || 100);

    console.log(`${filterType} results from DB function:`, result);
    return result;
  } catch (error) {
    console.error(`Error getting filter counts for ${filterType}:`, error);
    return [];
  }
}

// Helper function to format labels
function formatLabel(column: string, value: string): string {
  switch (column) {
    case 'platform':
      return value === 'strackr' ? 'Strackr' : value.charAt(0).toUpperCase() + value.slice(1);
    case 'status':
    case 'transaction_type':
      return value.charAt(0).toUpperCase() + value.slice(1);
    default:
      return value;
  }
}

export async function GET(request: NextRequest) {
  try {
    // Don't cache while we're debugging
    const headers = {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    };

    console.log('Fetching filter options with improved batching method...');

    // Use our efficient database function to get all filter counts in parallel
    const [platforms, merchants, statuses, networks, transactionTypes] = await Promise.all([
      getFilterCounts('platform'),
      getFilterCounts('merchant_name', 50), // Limit merchants to top 50
      getFilterCounts('status'),
      getFilterCounts('network_name'),
      getFilterCounts('transaction_type')
    ]);

    console.log('Filter options fetched successfully:', {
      platforms: platforms.length,
      merchants: merchants.length,
      statuses: statuses.length,
      networks: networks.length,
      transactionTypes: transactionTypes.length
    });

    // Log the actual transaction types for debugging
    console.log('Transaction types found:', transactionTypes);

    const response: FilterOptionsResponse = {
      platforms,
      merchants,
      statuses,
      networks,
      transactionTypes
    };

    return NextResponse.json(response, { headers });

  } catch (error) {
    console.error('Filter options API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
